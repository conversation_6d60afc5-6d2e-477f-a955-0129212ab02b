import maplibregl from 'maplibre-gl';
import { Protocol } from 'pmtiles';
import { createMapStyle } from '../assets/map-style.js';

const urlParams = new URLSearchParams(window.location.search);
const lng = parseFloat(urlParams.get('lng')) || 103.8198;
const lat = parseFloat(urlParams.get('lat')) || 1.3521;

let protocol = new Protocol();
maplibregl.addProtocol('pmtiles', protocol.tile);

const mapStyle = createMapStyle({ lang: 'en' });

const map = new maplibregl.Map({
  container: 'map',
  style: mapStyle,
  center: [lng, lat],
  zoom: 17,
  pitch: 60,
  interactive: false,
  attributionControl: false,
});
